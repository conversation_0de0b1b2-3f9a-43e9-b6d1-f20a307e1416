import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:ui';

class ErrorHandler {
  // منع إنشاء نسخة من الكلاس
  ErrorHandler._();
  
  // تهيئة معالج الأخطاء
  static Future<void> initialize() async {
    // يمكن إضافة تهيئة لخدمات تتبع الأخطاء مثل Firebase Crashlytics
    FlutterError.onError = (FlutterErrorDetails details) {
      FlutterError.presentError(details);
      reportError(details.exception, details.stack);
    };
  }
  
  // تسجيل الخطأ
  static void reportError(dynamic error, StackTrace? stackTrace) {
    if (kDebugMode) {
      print('خطأ: $error');
      if (stackTrace != null) {
        print('تتبع الخطأ: $stackTrace');
      }
    }
    
    // في الإنتاج، يمكن إرسال الخطأ إلى خدمة تتبع الأخطاء
    // مثال: FirebaseCrashlytics.instance.recordError(error, stackTrace);
  }
  
  // معالجة أخطاء Supabase
  static String handleSupabaseError(dynamic error) {
    if (error == null) {
      return 'حدث خطأ غير معروف';
    }
    
    if (error.toString().contains('Invalid login credentials')) {
      return 'بيانات تسجيل الدخول غير صحيحة';
    }
    
    if (error.toString().contains('Email not confirmed')) {
      return 'يرجى تأكيد البريد الإلكتروني أولاً';
    }
    
    if (error.toString().contains('User already registered')) {
      return 'البريد الإلكتروني مسجل بالفعل';
    }
    
    return 'حدث خطأ: ${error.toString()}';
  }
}


