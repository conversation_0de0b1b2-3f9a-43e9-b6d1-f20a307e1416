import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:hue/core/services/error_handler.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  final SupabaseClient _client = Supabase.instance.client;
  
  // إضافة بيانات
  Future<Map<String, dynamic>?> insertData(
    String table, 
    Map<String, dynamic> data
  ) async {
    try {
      final response = await _client
          .from(table)
          .insert(data)
          .select()
          .single();
      
      return response;
    } catch (e) {
      ErrorHandler.reportError(e, null);
      return null;
    }
  }
  
  // تحديث البيانات
  Future<Map<String, dynamic>?> updateData(
    String table, 
    String id, 
    Map<String, dynamic> data
  ) async {
    try {
      final response = await _client
          .from(table)
          .update(data)
          .eq('id', id)
          .select()
          .single();
      
      return response;
    } catch (e) {
      ErrorHandler.reportError(e, null);
      return null;
    }
  }
  
  // حذف البيانات
  Future<bool> deleteData(String table, String id) async {
    try {
      await _client
          .from(table)
          .delete()
          .eq('id', id);
      
      return true;
    } catch (e) {
      ErrorHandler.reportError(e, null);
      return false;
    }
  }
  
  // استرجاع بيانات
  Future<List<Map<String, dynamic>>?> getData(
    String table, {
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int? limit,
  }) async {
    try {
      var query = _client.from(table).select();
      
      // إضافة الفلاتر
      if (filters != null) {
        for (final entry in filters.entries) {
          query = query.eq(entry.key, entry.value);
        }
      }
      
      // إضافة الترتيب
      if (orderBy != null) {
        query = query.order(orderBy, ascending: ascending);
      }
      
      // إضافة الحد
      if (limit != null) {
        query = query.limit(limit);
      }
      
      final response = await query;
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      ErrorHandler.reportError(e, null);
      return null;
    }
  }
  
  // استرجاع عنصر واحد
  Future<Map<String, dynamic>?> getItem(
    String table,
    String id,
  ) async {
    try {
      final response = await _client
          .from(table)
          .select()
          .eq('id', id)
          .single();
      
      return response;
    } catch (e) {
      ErrorHandler.reportError(e, null);
      return null;
    }
  }
}


