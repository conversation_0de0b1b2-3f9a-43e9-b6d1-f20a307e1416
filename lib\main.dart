import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:get/get.dart';
import 'package:hue/Pages/home/<USER>';
import 'package:hue/core/utils/app_theme.dart';
import 'package:hue/core/utils/constants.dart';

void main() async {
  // ضمان تهيئة Flutter
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // تهيئة Supabase
    await Supabase.initialize(
      url: Constants.supabaseUrl,
      anonKey: Constants.supabaseAnonKey,
    );
    
    // تعيين معالج الأخطاء العام
    FlutterError.onError = (FlutterErrorDetails details) {
      FlutterError.presentError(details);
      print('خطأ في التطبيق: ${details.exception}');
    };
    
    runApp(const HueApp());
  } catch (e) {
    print('خطأ في تهيئة التطبيق: $e');
    runApp(const ErrorApp());
  }
}

class HueApp extends StatelessWidget {
  const HueApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Hue',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: const WelcomePage(),
      builder: (context, widget) {
        if (widget == null) {
          return const Scaffold(
            body: Center(
              child: Text('حدث خطأ غير متوقع'),
            ),
          );
        }
        
        return widget;
      },
    );
  }
}

class ErrorApp extends StatelessWidget {
  const ErrorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.red[700]),
              const SizedBox(height: 16),
              const Text(
                'حدث خطأ أثناء تشغيل التطبيق',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  main();
                },
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
