import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

class SecureStorageService {
  // استخدام SharedPreferences بدلاً من مكتبات التخزين الآمن المتخصصة
  
  // حفظ بيانات آمنة
  Future<bool> saveSecureData(String key, String value, {String? password}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // في حالة وجود كلمة مرور، يمكن تنفيذ تشفير بسيط
      // هذا مجرد مثال بسيط، في الإنتاج يجب استخدام مكتبات تشفير حقيقية
      String dataToSave = value;
      if (password != null) {
        dataToSave = _simpleEncrypt(value, password);
      }
      
      return await prefs.setString(key, dataToSave);
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في حفظ البيانات الآمنة: $e');
      }
      return false;
    }
  }
  
  // استرجاع بيانات آمنة
  Future<String?> getSecureData(String key, {String? password}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getString(key);
      
      if (value == null) return null;
      
      // فك التشفير إذا تم توفير كلمة مرور
      if (password != null) {
        return _simpleDecrypt(value, password);
      }
      
      return value;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في استرجاع البيانات الآمنة: $e');
      }
      return null;
    }
  }
  
  // حذف بيانات آمنة
  Future<bool> deleteSecureData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(key);
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في حذف البيانات الآمنة: $e');
      }
      return false;
    }
  }
  
  // التحقق من وجود بيانات
  Future<bool> containsKey(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey(key);
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في التحقق من وجود البيانات: $e');
      }
      return false;
    }
  }
  
  // حذف جميع البيانات
  Future<bool> clearAll() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.clear();
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في حذف جميع البيانات: $e');
      }
      return false;
    }
  }
  
  // تشفير بسيط (للتوضيح فقط - غير آمن للإنتاج)
  String _simpleEncrypt(String text, String password) {
    // تنفيذ تشفير بسيط جدًا للتوضيح
    final List<int> textBytes = text.codeUnits;
    final List<int> passwordBytes = password.codeUnits;
    final List<int> encrypted = [];
    
    for (int i = 0; i < textBytes.length; i++) {
      final int passwordIndex = i % passwordBytes.length;
      encrypted.add(textBytes[i] ^ passwordBytes[passwordIndex]);
    }
    
    return String.fromCharCodes(encrypted);
  }
  
  // فك التشفير البسيط
  String _simpleDecrypt(String encryptedText, String password) {
    // فك التشفير البسيط
    final List<int> encryptedBytes = encryptedText.codeUnits;
    final List<int> passwordBytes = password.codeUnits;
    final List<int> decrypted = [];
    
    for (int i = 0; i < encryptedBytes.length; i++) {
      final int passwordIndex = i % passwordBytes.length;
      decrypted.add(encryptedBytes[i] ^ passwordBytes[passwordIndex]);
    }
    
    return String.fromCharCodes(decrypted);
  }
}

