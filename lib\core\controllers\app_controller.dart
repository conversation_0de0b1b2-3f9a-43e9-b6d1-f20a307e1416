import 'package:get/get.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:hue/core/services/error_handler.dart';
import 'package:hue/core/services/secure_storage_service.dart';

class AppController extends GetxController {
  static AppController get to => Get.find();
  
  // حالة تحميل التطبيق
  final RxBool isLoading = false.obs;
  
  // حالة المصادقة
  final RxBool isLoggedIn = false.obs;
  
  // معلومات المستخدم
  final Rx<Map<String, dynamic>> userData = Rx<Map<String, dynamic>>({});
  
  // مرجع Supabase
  final supabase = Supabase.instance.client;
  
  @override
  void onInit() {
    super.onInit();
    checkLoginStatus();
  }
  
  // التحقق من حالة تسجيل الدخول
  Future<void> checkLoginStatus() async {
    isLoading.value = true;
    
    try {
      // التحقق من وجود جلسة نشطة في Supabase
      final session = supabase.auth.currentSession;
      
      if (session != null && session.isExpired == false) {
        isLoggedIn.value = true;
        await getUserData();
      } else {
        isLoggedIn.value = false;
        userData.value = {};
      }
    } catch (e) {
      isLoggedIn.value = false;
      ErrorHandler.reportError(e, null);
    } finally {
      isLoading.value = false;
    }
  }
  
  // الحصول على بيانات المستخدم
  Future<void> getUserData() async {
    try {
      if (!isLoggedIn.value) return;
      
      final userId = supabase.auth.currentUser?.id;
      if (userId == null) return;
      
      final response = await supabase
          .from('profiles')
          .select()
          .eq('id', userId)
          .single();
      
      userData.value = response;
    } catch (e) {
      ErrorHandler.reportError(e, null);
    }
  }
  
  // تسجيل الخروج
  Future<void> logout() async {
    isLoading.value = true;
    
    try {
      await supabase.auth.signOut();
      isLoggedIn.value = false;
      userData.value = {};
    } catch (e) {
      ErrorHandler.reportError(e, null);
    } finally {
      isLoading.value = false;
    }
  }
}


