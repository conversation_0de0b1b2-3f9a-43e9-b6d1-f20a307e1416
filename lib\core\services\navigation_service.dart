import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NavigationService {
  // التنقل إلى صفحة جديدة
  static Future<T?> navigateTo<T>(Widget page, {bool replace = false}) {
    if (replace) {
      return Get.off(() => page);
    } else {
      return Get.to(() => page);
    }
  }

  // التنقل إلى صفحة جديدة وحذف كل الصفحات السابقة
  static Future<T?> navigateAndRemoveUntil<T>(Widget page) {
    return Get.offAll(() => page);
  }

  // الرجوع للصفحة السابقة
  static void goBack<T>([T? result]) {
    Get.back(result: result);
  }

  // عرض رسالة تنبيه
  static Future<bool?> showConfirmationDialog({
    required String title,
    required String message,
    String confirmText = 'نعم',
    String cancelText = 'إلغاء',
  }) async {
    return await Get.dialog<bool>(
      AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  // عرض رسالة خطأ
  static void showErrorSnackbar(String message) {
    Get.snackbar(
      'خطأ',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      margin: const EdgeInsets.all(8),
      duration: const Duration(seconds: 3),
    );
  }

  // عرض رسالة نجاح
  static void showSuccessSnackbar(String message) {
    Get.snackbar(
      'نجاح',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      margin: const EdgeInsets.all(8),
      duration: const Duration(seconds: 3),
    );
  }

  // عرض مؤشر التحميل
  static void showLoading({String message = 'جاري التحميل...'}) {
    Get.dialog(
      Dialog(
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(message),
              ],
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  // إخفاء مؤشر التحميل
  static void hideLoading() {
    if (Get.isDialogOpen ?? false) {
      Get.back();
    }
  }
}

