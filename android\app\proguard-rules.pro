# قواعد Flutter الافتراضية
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# قواعد Supabase
-keep class io.supabase.** { *; }
-keep class com.google.crypto.tink.** { *; }

# قواعد للحفاظ على الموديلات
-keep class com.yourapp.models.** { *; }