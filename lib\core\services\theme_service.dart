import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeService {
  // مفتاح تخزين وضع السمة
  static const String themeKey = 'theme_mode';
  
  // الحصول على وضع السمة المخزن
  static Future<ThemeMode> getThemeMode() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      int? themeMode = prefs.getInt(themeKey);
      
      if (themeMode == null) {
        return ThemeMode.system;
      }
      
      return ThemeMode.values[themeMode];
    } catch (e) {
      print('خطأ في الحصول على وضع السمة: $e');
      return ThemeMode.system;
    }
  }
  
  // حفظ وضع السمة
  static Future<void> saveThemeMode(ThemeMode mode) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setInt(themeKey, mode.index);
    } catch (e) {
      print('خطأ في حفظ وضع السمة: $e');
    }
  }
  
  // تبديل وضع السمة
  static Future<void> toggleTheme() async {
    try {
      final currentTheme = Get.isDarkMode ? ThemeMode.light : ThemeMode.dark;
      Get.changeThemeMode(currentTheme);
      await saveThemeMode(currentTheme);
    } catch (e) {
      print('خطأ في تبديل وضع السمة: $e');
    }
  }
  
  // تطبيق وضع السمة المحفوظ
  static Future<void> applyStoredTheme() async {
    try {
      final themeMode = await getThemeMode();
      Get.changeThemeMode(themeMode);
    } catch (e) {
      print('خطأ في تطبيق وضع السمة المحفوظ: $e');
    }
  }
  
  // التحقق مما إذا كان الوضع الداكن مفعل
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }
}