import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_colors.dart';

class ThemeService {
  final _box = SharedPreferences.getInstance();
  final _key = 'isDarkMode';

  ThemeMode get theme => _loadThemeFromBox() ? ThemeMode.dark : ThemeMode.light;

  bool _loadThemeFromBox() async {
    final prefs = await _box;
    return prefs.getBool(_key) ?? false;
  }

  _saveThemeToBox(bool isDarkMode) async {
    final prefs = await _box;
    prefs.setBool(_key, isDarkMode);
  }

  void switchTheme() {
    Get.changeThemeMode(_loadThemeFromBox() ? ThemeMode.light : ThemeMode.dark);
    _saveThemeToBox(!_loadThemeFromBox());
  }

  static ThemeData get lightTheme {
    return ThemeData(
      brightness: Brightness.light,
      primaryColor: mainColors.primaryColor,
      colorScheme: ColorScheme.light(
        primary: mainColors.primaryColor,
        secondary: mainColors.accentColor,
        error: mainColors.errorColor,
      ),
      scaffoldBackgroundColor: mainColors.scaffoldBackgroundColor,
      appBarTheme: AppBarTheme(
        backgroundColor: mainColors.appBarBackgroundColor,
        foregroundColor: mainColors.appBarForegroundColor,
        elevation: 2,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: mainColors.navBarBackground,
        selectedItemColor: mainColors.navBarSelectedItem,
        unselectedItemColor: mainColors.navBarUnselectedItem.withOpacity(0.7),
        elevation: 8,
      ),
      cardTheme: CardTheme(
        color: mainColors.cardBackground,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      textTheme: TextTheme(
        titleLarge: TextStyle(color: mainColors.titleTextColor),
        titleMedium: TextStyle(color: mainColors.titleTextColor),
        bodyLarge: TextStyle(color: mainColors.bodyTextColor),
        bodyMedium: TextStyle(color: mainColors.bodyTextColorMedium),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      brightness: Brightness.dark,
      primaryColor: mainColors.primaryColorDark,
      colorScheme: ColorScheme.dark(
        primary: mainColors.primaryColorDark,
        secondary: mainColors.accentColorDark,
        error: mainColors.errorColorDark,
      ),
      scaffoldBackgroundColor: mainColors.scaffoldBackgroundColorDark,
      appBarTheme: AppBarTheme(
        backgroundColor: mainColors.appBarBackgroundColorDark,
        foregroundColor: mainColors.appBarForegroundColorDark,
        elevation: 2,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: mainColors.navBarBackgroundDark,
        selectedItemColor: mainColors.navBarSelectedItemDark,
        unselectedItemColor: mainColors.navBarUnselectedItemDark.withOpacity(0.7),
        elevation: 8,
      ),
      cardTheme: CardTheme(
        color: mainColors.cardBackgroundDark,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      textTheme: TextTheme(
        titleLarge: TextStyle(color: mainColors.titleTextColorDark),
        titleMedium: TextStyle(color: mainColors.titleTextColorDark),
        bodyLarge: TextStyle(color: mainColors.bodyTextColorDark),
        bodyMedium: TextStyle(color: mainColors.bodyTextColorMediumDark),
      ),
    );
  }
}