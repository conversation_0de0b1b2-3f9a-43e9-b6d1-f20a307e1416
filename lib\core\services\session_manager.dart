import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:hue/core/utils/constants.dart';
import 'package:hue/core/services/secure_storage_service.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:uuid/uuid.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'package:hue/core/services/error_handler.dart';
import 'dart:io';

class SessionManager {
  static final SessionManager _instance = SessionManager._internal();
  factory SessionManager() => _instance;
  SessionManager._internal();
  
  final SupabaseClient _supabase = Supabase.instance.client;
  final SecureStorageService _secureStorage = SecureStorageService();
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  
  // التحقق من وجود جلسة نشطة وصالحة
  Future<bool> hasActiveSession() async {
    try {
      final session = await _supabase.auth.getSession();
      
      if (session.data.session == null) {
        return false;
      }
      
      // التحقق من صلاحية الجلسة
      final token = session.data.session!.accessToken;
      if (JwtDecoder.isExpired(token)) {
        await _clearSession();
        return false;
      }
      
      // التحقق من تطابق معرف الجهاز
      final storedDeviceId = await _secureStorage.getSecureData(Constants.deviceIdKey);
      final currentDeviceId = await _getDeviceId();
      
      if (storedDeviceId != currentDeviceId) {
        print('Security warning: Device ID mismatch');
        await _clearSession();
        return false;
      }
      
      return true;
    } catch (e) {
      print('Error checking session: $e');
      return false;
    }
  }
  
  // الحصول على المستخدم الحالي
  User? get currentUser => _supabase.auth.currentUser;
  
  // حفظ بيانات الجلسة بشكل آمن
  Future<void> saveSession(Session session) async {
    try {
      // إنشاء مفتاح تشفير فريد
      final deviceId = await _getDeviceId();
      final uuid = const Uuid().v4();
      final encryptionKey = _generateEncryptionKey(deviceId, uuid);
      
      // حفظ UUID للاستخدام في فك التشفير
      await _secureStorage.saveSecureData(Constants.sessionUuidKey, uuid);
      
      // تشفير وحفظ بيانات الجلسة
      await _secureStorage.saveSecureData(
        Constants.userSessionKey, 
        session.persistSessionString,
        password: encryptionKey,
      );
      
      // تسجيل معلومات الجلسة
      await _logSessionActivity(session, 'login');
    } catch (e) {
      ErrorHandler.reportError(e, null);
    }
  }
  
  // حذف بيانات الجلسة
  Future<void> clearSession() async {
    try {
      await _secureStorage.deleteSecureData(Constants.userSessionKey);
      await _secureStorage.deleteSecureData(Constants.sessionUuidKey);
      
      // تسجيل تسجيل الخروج
      final currentSession = _supabase.auth.currentSession;
      if (currentSession != null) {
        await _logSessionActivity(currentSession, 'logout');
      }
    } catch (e) {
      ErrorHandler.reportError(e, null);
    }
  }
  
  // الحصول على معرف الجهاز
  Future<String> _getDeviceId() async {
    try {
      // التحقق من وجود معرف مخزن
      final storedDeviceId = await _secureStorage.getSecureData(Constants.deviceIdKey);
      if (storedDeviceId != null) {
        return storedDeviceId;
      }
      
      // إنشاء معرف جديد
      String deviceId;
      
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        deviceId = androidInfo.id;
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        deviceId = iosInfo.identifierForVendor ?? const Uuid().v4();
      } else {
        deviceId = const Uuid().v4();
      }
      
      // تخزين المعرف
      await _secureStorage.saveSecureData(Constants.deviceIdKey, deviceId);
      return deviceId;
    } catch (e) {
      ErrorHandler.reportError(e, null);
      // في حالة الفشل، إنشاء معرف مؤقت
      return const Uuid().v4();
    }
  }
  
  // إنشاء مفتاح تشفير
  String _generateEncryptionKey(String deviceId, String uuid) {
    final data = utf8.encode('$deviceId:$uuid:${Constants.encryptionSalt}');
    final hash = sha256.convert(data);
    return hash.toString();
  }
  
  // تسجيل نشاط الجلسة للكشف عن الاختراقات
  Future<void> _logSessionActivity(Session session, String activity) async {
    try {
      final deviceId = await _getDeviceId();
      
      await _supabase.from('session_logs').insert({
        'user_id': session.user.id,
        'device_id': deviceId,
        'activity': activity,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      // تجاهل أخطاء التسجيل
      ErrorHandler.reportError(e, null);
    }
  }
  
  // استعادة الجلسة
  Future<Session?> getSession() async {
    try {
      // الحصول على المعلومات اللازمة لفك التشفير
      final deviceId = await _getDeviceId();
      final uuid = await _secureStorage.getSecureData(Constants.sessionUuidKey);
      
      if (uuid == null) return null;
      
      final encryptionKey = _generateEncryptionKey(deviceId, uuid);
      
      // استرجاع بيانات الجلسة المشفرة
      final sessionData = await _secureStorage.getSecureData(
        Constants.userSessionKey,
        password: encryptionKey,
      );
      
      if (sessionData == null) return null;
      
      // إنشاء كائن الجلسة
      return Session.fromPersistSessionString(sessionData);
    } catch (e) {
      ErrorHandler.reportError(e, null);
      return null;
    }
  }

  // إضافة تسجيل خروج تلقائي بعد فترة من عدم النشاط
  Future<void> setupAutoLogout(BuildContext context) async {
    _activityTimer = Timer(const Duration(minutes: 30), () {
      logout(context);
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (_) => const LoginPage()),
        (route) => false,
      );
      _showSessionExpiredDialog(context);
    });
  }

  // تحديث وقت النشاط عند تفاعل المستخدم
  void updateUserActivity() {
    if (_activityTimer != null) {
      _activityTimer!.cancel();
      setupAutoLogout(Get.context!);
    }
  }

  // تسجيل محاولات الوصول غير المصرح بها
  Future<void> logUnauthorizedAccess(String reason) async {
    final deviceInfo = await _getDeviceInfo();
    await _securityService.logSecurityEvent(
      'UNAUTHORIZED_ACCESS',
      {
        'reason': reason,
        'deviceInfo': deviceInfo,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }
}









